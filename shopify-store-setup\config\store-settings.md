# Shopify Store Configuration Guide

## 1. Basic Store Settings

### Store Information
- **Store Name**: HairMart Pro (or your preferred name)
- **Store Description**: Premium hair care products for healthy, beautiful hair
- **Store Address**: Complete your business address for tax/shipping calculations
- **Currency**: Set your local currency
- **Timezone**: Set your business timezone

### Legal Pages (Required)
Create these pages in Shopify Admin > Online Store > Pages:

1. **Privacy Policy**
2. **Terms of Service** 
3. **Refund Policy**
4. **Shipping Policy**

## 2. Theme Selection & Customization

### Recommended Themes for Hair Products:
1. **Dawn** (Free) - Clean, fast, mobile-first
2. **Craft** (Paid) - Perfect for beauty products
3. **Prestige** (Paid) - Luxury feel for premium products
4. **Impulse** (Paid) - Great for product showcasing

### Theme Customization Priorities:
1. **Header**: Clean navigation with search bar
2. **Hero Section**: High-quality hair model images
3. **Product Grid**: Large, clear product images
4. **Footer**: Contact info, social links, policies

## 3. Essential Apps to Install

### Must-Have Apps:
1. **Product Reviews** - Shopify Product Reviews (Free)
2. **SEO Manager** - TinyIMG or Plug in SEO
3. **Email Marketing** - Shopify Email or Klaviyo
4. **Live Chat** - Shopify Inbox (Free)
5. **Analytics** - Google Analytics & Facebook Pixel

### Hair Product Specific Apps:
1. **Subscription** - ReCharge (for recurring orders)
2. **Quiz Builder** - Hair type/concern quiz
3. **Before/After Gallery** - Customer results showcase
4. **Ingredient Glossary** - Product education

## 4. Payment & Shipping Setup

### Payment Gateways:
- Enable Shopify Payments (lowest fees)
- Add PayPal Express
- Consider Apple Pay/Google Pay
- Add "Buy now, pay later" options (Klarna, Afterpay)

### Shipping Zones:
1. Set up domestic shipping rates
2. Configure international shipping (if applicable)
3. Add free shipping threshold (e.g., $50+)
4. Set up local delivery/pickup options

## 5. Tax Configuration
- Enable automatic tax calculation
- Set up tax-exempt customers (if applicable)
- Configure tax overrides for specific products

## Next Steps
After basic setup, proceed to product catalog creation and custom styling.
