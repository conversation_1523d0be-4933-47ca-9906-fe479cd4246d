/* HairMart Pro - Custom Shopify Styles */
/* Add this CSS to your theme's custom CSS section */

/* ===== GLOBAL STYLES ===== */
:root {
  --primary-color: #8B4513; /* Rich brown for hair theme */
  --secondary-color: #D2691E; /* Warm orange-brown */
  --accent-color: #FFD700; /* Gold for premium feel */
  --text-dark: #2C1810; /* Dark brown text */
  --text-light: #F5F5DC; /* Cream text */
  --background-light: #FFF8DC; /* Cornsilk background */
  --border-color: #DEB887; /* Burlywood borders */
}

/* Enhanced Typography */
.product-title {
  font-family: 'Playfair Display', serif;
  font-weight: 600;
  color: var(--text-dark);
  line-height: 1.3;
}

.product-description {
  font-family: 'Source Sans Pro', sans-serif;
  line-height: 1.6;
  color: var(--text-dark);
}

/* ===== HEADER ENHANCEMENTS ===== */
.site-header {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.site-nav a {
  color: var(--text-light);
  font-weight: 500;
  transition: color 0.3s ease;
}

.site-nav a:hover {
  color: var(--accent-color);
}

/* ===== PRODUCT CARDS ===== */
.product-card {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  background: white;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.product-image {
  position: relative;
  overflow: hidden;
}

.product-image img {
  transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

/* Product Badge Styles */
.product-badge {
  position: absolute;
  top: 10px;
  left: 10px;
  background: var(--accent-color);
  color: var(--text-dark);
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
}

.product-badge.new {
  background: #28a745;
  color: white;
}

.product-badge.sale {
  background: #dc3545;
  color: white;
}

/* ===== HERO SECTION ===== */
.hero-section {
  background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)), 
              url('hero-hair-background.jpg');
  background-size: cover;
  background-position: center;
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: white;
}

.hero-content h1 {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.hero-content p {
  font-size: 1.3rem;
  margin-bottom: 2rem;
  max-width: 600px;
}

/* ===== BUTTONS ===== */
.btn-primary {
  background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
  border: none;
  padding: 12px 30px;
  border-radius: 25px;
  color: white;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.3);
  background: linear-gradient(45deg, var(--secondary-color), var(--primary-color));
}

.btn-secondary {
  background: transparent;
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
  padding: 10px 25px;
  border-radius: 25px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: var(--primary-color);
  color: white;
}

/* ===== COLLECTION GRID ===== */
.collection-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  padding: 40px 0;
}

/* ===== TESTIMONIALS ===== */
.testimonial-card {
  background: var(--background-light);
  padding: 30px;
  border-radius: 15px;
  border-left: 4px solid var(--accent-color);
  margin: 20px 0;
  box-shadow: 0 4px 10px rgba(0,0,0,0.1);
}

.testimonial-text {
  font-style: italic;
  font-size: 1.1rem;
  margin-bottom: 15px;
  color: var(--text-dark);
}

.testimonial-author {
  font-weight: 600;
  color: var(--primary-color);
}

/* ===== MOBILE RESPONSIVENESS ===== */
@media (max-width: 768px) {
  .hero-content h1 {
    font-size: 2.5rem;
  }
  
  .hero-content p {
    font-size: 1.1rem;
  }
  
  .collection-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .product-card {
    margin-bottom: 20px;
  }
}

/* ===== LOADING ANIMATIONS ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* ===== HAIR-SPECIFIC ELEMENTS ===== */
.hair-type-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin: 20px 0;
}

.hair-type-option {
  padding: 8px 16px;
  border: 2px solid var(--border-color);
  border-radius: 20px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.hair-type-option:hover,
.hair-type-option.selected {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* Ingredient Highlight */
.ingredient-highlight {
  background: linear-gradient(120deg, transparent 0%, var(--accent-color) 0%, var(--accent-color) 100%, transparent 100%);
  background-size: 0% 100%;
  background-repeat: no-repeat;
  transition: background-size 0.3s ease;
  padding: 2px 4px;
  border-radius: 3px;
}

.ingredient-highlight:hover {
  background-size: 100% 100%;
  color: var(--text-dark);
}
